/**
 * policyExtractor.js - Utility functions for extracting policy information from PNR data
 * This file handles the extraction of Cover Genius policy IDs from FlyDubai PNR responses
 */

/**
 * Extracts Cover Genius policy IDs from the PNR data
 * @param {Object} pnrData - The parsed PNR data object
 * @returns {Array} - Array of policy IDs
 */
function extractCoverGeniusPolicyIds(pnrData) {
    const policyIds = [];
    const seen = new Set(); // To prevent duplicate policy IDs
    
    // Check each paxSegment for insurance information
    if (pnrData && pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {
        pnrData.paxSegments.forEach(paxSegment => {
            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
                paxSegment.recordDetails.forEach(record => {
                    if (record.insuTransID && 
                        (!record.provider || record.provider !== 'AIG')) {
                        // Extract the policy ID from the insurance transaction ID
                        const parts = record.insuTransID.split('/');
                        if (parts.length > 0) {
                            const policyId = parts[0];
                            // Add to array if not already added
                            if (!seen.has(policyId)) {
                                policyIds.push(policyId);
                                seen.add(policyId);
                                console.log(`Found Cover Genius policy ID: ${policyId}`);
                            }
                        }
                    }
                });
            }
        });
    }
    
    return policyIds;
}

// Export the function for use in other modules
module.exports = {
    extractCoverGeniusPolicyIds
};
