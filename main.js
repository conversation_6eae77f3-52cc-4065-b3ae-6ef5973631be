/**
 * Main Application - Cover Genius Policy Analysis
 *
 * This application orchestrates the complete workflow:
 * 1. Accepts a PNR number
 * 2. Retrieves PNR data from FlyDubai API
 * 3. Extracts Cover Genius policy IDs
 * 4. Fetches policy details from Cover Genius API
 * 5. Generates comprehensive HTML report
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = require('./integrations');
const { extractCoverGeniusPolicyIds } = require('./functions');
const { generateHtmlReport } = require('./reports');
const { statusCodeMap, channelCodeMap } = require('./constants');
const moment = require('moment');

/**
 * Processes insurance records from PNR data and Cover Genius policy data
 * @param {Object} pnrData - The PNR data from FlyDubai API
 * @param {Object} policyData - The policy data from Cover Genius API
 * @param {string} policyId - The policy ID
 * @returns {Object} - Processed data for report generation
 */
function processInsuranceData(pnrData, policyData, policyId) {
    const insuranceRecords = [];

    // Extract policy dates from Cover Genius response
    let policyStartDate = null;
    let policyEndDate = null;

    // Handle different possible Cover Genius response structures
    if (policyData && policyData.data) {
        // Try different possible structures
        if (policyData.data.quotes && policyData.data.quotes.length > 0) {
            // Structure: data.quotes[0].policy_start_date
            const quote = policyData.data.quotes[0];
            if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
            if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
        } else if (policyData.data.policy) {
            // Structure: data.policy.start_date
            const policy = policyData.data.policy;
            if (policy.start_date) policyStartDate = moment(policy.start_date);
            if (policy.end_date) policyEndDate = moment(policy.end_date);
        }
    } else if (policyData && policyData.quotes && policyData.quotes.length > 0) {
        // Direct quotes structure
        const quote = policyData.quotes[0];
        if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
        if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
    }

    // Function to check if a date falls within policy period
    function isWithinPolicyPeriod(date) {
        if (!date || !policyStartDate || !policyEndDate) return "Unknown";
        const momentDate = moment(date);
        return momentDate.isSameOrAfter(policyStartDate) && momentDate.isSameOrBefore(policyEndDate);
    }

    // Create a mapping of passengers
    const passengersMap = {};
    if (pnrData.persons && Array.isArray(pnrData.persons)) {
        pnrData.persons.forEach(person => {
            passengersMap[person.paxID] = {
                fullName: `${person.title || ''} ${person.fName || ''} ${person.lName || ''}`.trim(),
                recNums: person.recNum || []
            };
        });
    }

    // Build segment information
    const segmentsMap = {};
    if (pnrData.segments && Array.isArray(pnrData.segments)) {
        pnrData.segments.forEach(segment => {
            segmentsMap[segment.segKey] = {
                origin: segment.org,
                destination: segment.dest,
                departureTime: segment.depTime,
                arrivalTime: segment.arrTime,
                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`,
                persons: segment.persons || []
            };
        });
    }

    // Create a map to link record numbers to segments
    const recNumToSegmentMap = {};
    if (pnrData.segments && Array.isArray(pnrData.segments)) {
        pnrData.segments.forEach(segment => {
            if (segment.persons && Array.isArray(segment.persons)) {
                segment.persons.forEach(person => {
                    recNumToSegmentMap[person.recNum] = segment;
                });
            }
        });
    }

    // Build a mapping between recNum and person IDs
    const recNumToPersonMap = {};
    if (pnrData.persons && Array.isArray(pnrData.persons)) {
        pnrData.persons.forEach(person => {
            if (person.recNum && Array.isArray(person.recNum)) {
                person.recNum.forEach(recNum => {
                    recNumToPersonMap[recNum] = person.paxID;
                });
            }
        });
    }

    // Process paxSegments data to find records with insurance
    if (pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {
        pnrData.paxSegments.forEach(paxSegment => {
            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
                paxSegment.recordDetails.forEach(record => {
                    if (record.insuTransID) {
                        // Find passenger ID from record number
                        const paxId = recNumToPersonMap[paxSegment.recNum];

                        // Build full insurance record with relevant information
                        const insuranceInfo = {
                            recordNumber: paxSegment.recNum,
                            insuTransID: record.insuTransID,
                            insuConfNum: record.insuConfNum || null,
                            statusCode: record.status,
                            statusText: statusCodeMap[record.status] || "Unknown",
                            channelCode: record.channelID,
                            channelText: channelCodeMap[record.channelID] || "Unknown",
                            provider: record.provider || "Unknown",
                            insuPurchaseDate: record.insuPurchasedate ?
                                moment(record.insuPurchasedate, "M/D/YYYY h:mm:ss A").format("YYYY-MM-DD HH:mm:ss") :
                                null,
                            paxId: paxId,
                            passengerName: (paxId && passengersMap[paxId]?.fullName) || "Unknown",
                            bookDate: record.bookDate,
                            hasConfirmation: !!record.insuConfNum,
                            segmentInfo: null,
                            departureDate: null,
                            withinPolicyPeriod: "Unknown",
                            matchesCoverGeniusPolicyId: record.insuTransID.includes(policyId)
                        };

                        // Get segment info from recNum
                        const segment = recNumToSegmentMap[paxSegment.recNum];
                        if (segment) {
                            // Format segment info in the expected structure
                            insuranceInfo.segmentInfo = {
                                origin: segment.org,
                                destination: segment.dest,
                                departureTime: segment.depTime,
                                arrivalTime: segment.arrTime,
                                flightNumber: `${segment.operCarrier}${segment.operFlightNum}`
                            };
                            insuranceInfo.departureDate = segment.depTime;
                        }

                        // Check if date is within policy period
                        insuranceInfo.withinPolicyPeriod = isWithinPolicyPeriod(insuranceInfo.departureDate);

                        insuranceRecords.push(insuranceInfo);
                    }
                });
            }
        });
    }

    return {
        insuranceRecords,
        policyStartDate: policyStartDate || moment(),
        policyEndDate: policyEndDate || moment()
    };
}

/**
 * Main function that orchestrates the entire workflow
 * @param {string} pnrNumber - The PNR number to process
 */
async function main(pnrNumber) {
    try {
        console.log('='.repeat(60));
        console.log('Cover Genius Policy Analysis - Starting Process');
        console.log('='.repeat(60));
        console.log(`Processing PNR: ${pnrNumber}`);
        console.log('');

        // Step 1: Retrieve PNR data
        console.log('Step 1: Retrieving PNR data from FlyDubai API...');
        const pnrData = await retrieveAndSavePNRData(pnrNumber);
        console.log('✓ PNR data retrieved successfully');
        console.log('');

        // Step 2: Extract Cover Genius policy IDs
        console.log('Step 2: Extracting Cover Genius policy IDs...');
        const policyIds = extractCoverGeniusPolicyIds(pnrData);

        if (policyIds.length === 0) {
            console.log('❌ No Cover Genius policy IDs found in PNR data');
            console.log('Process completed - no policies to analyze');
            return;
        }

        console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);
        console.log('');

        // Step 3: Process each policy ID
        for (let i = 0; i < policyIds.length; i++) {
            const policyId = policyIds[i];
            console.log(`Step 3.${i + 1}: Processing policy ID: ${policyId}`);

            try {
                // Fetch Cover Genius policy data
                console.log('  - Fetching policy details from Cover Genius API...');
                const policyResponse = await fetchCoverGeniusPolicy(policyId);
                console.log('  ✓ Policy data retrieved successfully');

                // Extract and log policy information
                let policyStartDate = null;
                let policyEndDate = null;
                if (policyResponse && policyResponse.data) {
                    if (policyResponse.data.quotes && policyResponse.data.quotes.length > 0) {
                        const quote = policyResponse.data.quotes[0];
                        if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
                        if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
                    } else if (policyResponse.data.policy) {
                        const policy = policyResponse.data.policy;
                        if (policy.start_date) policyStartDate = moment(policy.start_date);
                        if (policy.end_date) policyEndDate = moment(policy.end_date);
                    }
                } else if (policyResponse && policyResponse.quotes && policyResponse.quotes.length > 0) {
                    const quote = policyResponse.quotes[0];
                    if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
                    if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
                }

                if (policyStartDate && policyEndDate) {
                    console.log(`  - Policy period: ${policyStartDate.format("YYYY-MM-DD HH:mm:ss")} to ${policyEndDate.format("YYYY-MM-DD HH:mm:ss")}`);
                }

                // Process the data
                console.log('  - Processing insurance data...');
                const processedData = processInsuranceData(pnrData, policyResponse, policyId);
                console.log(`  ✓ Processed ${processedData.insuranceRecords.length} insurance records`);

                // Log detailed analysis
                const missingConfirmations = processedData.insuranceRecords.filter(record => !record.hasConfirmation);
                const recordsWithMatchingPolicyId = processedData.insuranceRecords.filter(r => r.matchesCoverGeniusPolicyId);
                const recordsWithinPolicyPeriod = processedData.insuranceRecords.filter(r => r.withinPolicyPeriod === true);

                console.log(`  - Records missing confirmation: ${missingConfirmations.length}`);
                console.log(`  - Records matching policy ID: ${recordsWithMatchingPolicyId.length}`);
                console.log(`  - Records within policy period: ${recordsWithinPolicyPeriod.length}`);

                // Generate report
                console.log('  - Generating HTML report...');
                generateHtmlReport(
                    pnrNumber,
                    processedData.insuranceRecords,
                    processedData.policyStartDate,
                    processedData.policyEndDate,
                    policyId
                );
                console.log(`  ✓ Report generated for policy ${policyId}`);

            } catch (error) {
                console.error(`  ❌ Error processing policy ${policyId}:`, error.message);
                console.log(`  Skipping policy ${policyId} and continuing...`);
            }

            console.log('');
        }

        console.log('='.repeat(60));
        console.log('Process completed successfully!');
        console.log('='.repeat(60));

    } catch (error) {
        console.error('❌ Fatal error in main process:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

/**
 * Entry point - handles command line arguments
 */
function run() {
    // Get PNR from command line arguments
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Usage: node main.js <PNR_NUMBER>');
        console.log('Example: node main.js ABC123');
        process.exit(1);
    }

    const pnrNumber = args[0].toUpperCase().trim();

    if (!pnrNumber) {
        console.error('Error: PNR number cannot be empty');
        process.exit(1);
    }

    // Validate PNR format (basic validation)
    if (pnrNumber.length < 3 || pnrNumber.length > 10) {
        console.error('Error: PNR number should be between 3 and 10 characters');
        process.exit(1);
    }

    // Start the main process
    main(pnrNumber);
}

// Export functions for testing
module.exports = {
    main,
    processInsuranceData
};

// Run the application if this file is executed directly
if (require.main === module) {
    run();
}
