/**
 * Main Application - Cover Genius Policy Analysis
 * 
 * This application orchestrates the complete workflow:
 * 1. Accepts a PNR number
 * 2. Retrieves PNR data from FlyDubai API
 * 3. Extracts Cover Genius policy IDs
 * 4. Fetches policy details from Cover Genius API
 * 5. Generates comprehensive HTML report
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = require('./integrations');
const { extractCoverGeniusPolicyIds } = require('./functions');
const { generateHtmlReport } = require('./reports');
const moment = require('moment');

/**
 * Processes insurance records from PNR data and Cover Genius policy data
 * @param {Object} pnrData - The PNR data from FlyDubai API
 * @param {Object} policyData - The policy data from Cover Genius API
 * @param {string} policyId - The policy ID
 * @returns {Object} - Processed data for report generation
 */
function processInsuranceData(pnrData, policyData, policyId) {
    const insuranceRecords = [];
    
    // Extract policy dates from Cover Genius response
    let policyStartDate = null;
    let policyEndDate = null;
    
    if (policyData && policyData.data && policyData.data.policy) {
        const policy = policyData.data.policy;
        if (policy.start_date) {
            policyStartDate = moment(policy.start_date);
        }
        if (policy.end_date) {
            policyEndDate = moment(policy.end_date);
        }
    }
    
    // Process PNR data to create insurance records
    if (pnrData && pnrData.paxSegments && Array.isArray(pnrData.paxSegments)) {
        pnrData.paxSegments.forEach((paxSegment, segmentIndex) => {
            if (paxSegment.recordDetails && paxSegment.recordDetails.length > 0) {
                paxSegment.recordDetails.forEach((record, recordIndex) => {
                    if (record.insuTransID && 
                        (!record.provider || record.provider !== 'AIG')) {
                        
                        // Find passenger information
                        let passengerName = 'Unknown';
                        if (pnrData.persons && pnrData.persons.length > 0) {
                            const passenger = pnrData.persons.find(p => p.personNumber === paxSegment.personNumber);
                            if (passenger && passenger.names && passenger.names.length > 0) {
                                const name = passenger.names[0];
                                passengerName = `${name.firstName || ''} ${name.lastName || ''}`.trim();
                            }
                        }
                        
                        // Find segment information
                        let segmentInfo = null;
                        let departureDate = null;
                        if (pnrData.segments && pnrData.segments.length > 0) {
                            const segment = pnrData.segments.find(s => s.segmentNumber === paxSegment.segmentNumber);
                            if (segment) {
                                segmentInfo = {
                                    origin: segment.origin,
                                    destination: segment.destination,
                                    flightNumber: segment.flightNumber,
                                    departureDate: segment.departureDate
                                };
                                departureDate = segment.departureDate;
                            }
                        }
                        
                        // Check if purchase date is within policy period
                        let withinPolicyPeriod = null;
                        if (record.insuPurchaseDate && policyStartDate && policyEndDate) {
                            const purchaseDate = moment(record.insuPurchaseDate);
                            withinPolicyPeriod = purchaseDate.isBetween(policyStartDate, policyEndDate, null, '[]');
                        }
                        
                        // Create insurance record
                        const insuranceRecord = {
                            recordNumber: `${segmentIndex + 1}-${recordIndex + 1}`,
                            passengerName: passengerName,
                            segmentInfo: segmentInfo,
                            departureDate: departureDate,
                            insuTransID: record.insuTransID,
                            provider: record.provider || 'Cover Genius',
                            statusText: record.statusText || 'Unknown',
                            channelText: record.channelText || 'Unknown',
                            insuPurchaseDate: record.insuPurchaseDate || 'Unknown',
                            insuConfNum: record.insuConfNum || null,
                            hasConfirmation: !!(record.insuConfNum && record.insuConfNum.trim()),
                            withinPolicyPeriod: withinPolicyPeriod
                        };
                        
                        insuranceRecords.push(insuranceRecord);
                    }
                });
            }
        });
    }
    
    return {
        insuranceRecords,
        policyStartDate: policyStartDate || moment(),
        policyEndDate: policyEndDate || moment()
    };
}

/**
 * Main function that orchestrates the entire workflow
 * @param {string} pnrNumber - The PNR number to process
 */
async function main(pnrNumber) {
    try {
        console.log('='.repeat(60));
        console.log('Cover Genius Policy Analysis - Starting Process');
        console.log('='.repeat(60));
        console.log(`Processing PNR: ${pnrNumber}`);
        console.log('');
        
        // Step 1: Retrieve PNR data
        console.log('Step 1: Retrieving PNR data from FlyDubai API...');
        const pnrData = await retrieveAndSavePNRData(pnrNumber);
        console.log('✓ PNR data retrieved successfully');
        console.log('');
        
        // Step 2: Extract Cover Genius policy IDs
        console.log('Step 2: Extracting Cover Genius policy IDs...');
        const policyIds = extractCoverGeniusPolicyIds(pnrData);
        
        if (policyIds.length === 0) {
            console.log('❌ No Cover Genius policy IDs found in PNR data');
            console.log('Process completed - no policies to analyze');
            return;
        }
        
        console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);
        console.log('');
        
        // Step 3: Process each policy ID
        for (let i = 0; i < policyIds.length; i++) {
            const policyId = policyIds[i];
            console.log(`Step 3.${i + 1}: Processing policy ID: ${policyId}`);
            
            try {
                // Fetch Cover Genius policy data
                console.log('  - Fetching policy details from Cover Genius API...');
                const policyResponse = await fetchCoverGeniusPolicy(policyId);
                console.log('  ✓ Policy data retrieved successfully');
                
                // Process the data
                console.log('  - Processing insurance data...');
                const processedData = processInsuranceData(pnrData, policyResponse, policyId);
                console.log(`  ✓ Processed ${processedData.insuranceRecords.length} insurance records`);
                
                // Generate report
                console.log('  - Generating HTML report...');
                generateHtmlReport(
                    pnrNumber,
                    processedData.insuranceRecords,
                    processedData.policyStartDate,
                    processedData.policyEndDate,
                    policyId
                );
                console.log(`  ✓ Report generated for policy ${policyId}`);
                
            } catch (error) {
                console.error(`  ❌ Error processing policy ${policyId}:`, error.message);
                console.log(`  Skipping policy ${policyId} and continuing...`);
            }
            
            console.log('');
        }
        
        console.log('='.repeat(60));
        console.log('Process completed successfully!');
        console.log('='.repeat(60));
        
    } catch (error) {
        console.error('❌ Fatal error in main process:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

/**
 * Entry point - handles command line arguments
 */
function run() {
    // Get PNR from command line arguments
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node main.js <PNR_NUMBER>');
        console.log('Example: node main.js ABC123');
        process.exit(1);
    }
    
    const pnrNumber = args[0].toUpperCase().trim();
    
    if (!pnrNumber) {
        console.error('Error: PNR number cannot be empty');
        process.exit(1);
    }
    
    // Validate PNR format (basic validation)
    if (pnrNumber.length < 3 || pnrNumber.length > 10) {
        console.error('Error: PNR number should be between 3 and 10 characters');
        process.exit(1);
    }
    
    // Start the main process
    main(pnrNumber);
}

// Export functions for testing
module.exports = {
    main,
    processInsuranceData
};

// Run the application if this file is executed directly
if (require.main === module) {
    run();
}
