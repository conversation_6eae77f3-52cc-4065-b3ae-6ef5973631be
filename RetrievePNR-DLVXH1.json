{"seriesNum": "299", "PNR": "DLVXH1", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82063188", "bookDate": "2025-04-16T19:09:04", "modifyDate": "2025-05-12T10:35:01", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "4d4494a62cr1x2aa442jk7t6t688m84746204ad5d271", "securityGUID": "4d4494a62cr1x2aa442jk7t6t688m84746204ad5d271", "lastLoadGUID": "b719be92-e573-4f28-a7da-76fd497204c0", "isAsyncPNR": false, "MasterPNR": "DLVXH1", "segments": [{"segKey": "16108879:16108879:6/2/2025 10:25:00 PM", "LFID": 16108879, "depDate": "2025-06-02T00:00:00", "flightGroupId": "16108879", "org": "DOH", "dest": "CMB", "depTime": "2025-06-02T22:25:00", "depTimeGMT": "2025-06-02T19:25:00", "arrTime": "2025-06-03T08:10:00", "operCarrier": "FZ", "operFlightNum": "020/579", "mrktCarrier": "FZ ", "mrktFlightNum": "020/579", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181016, "depDate": "2025-06-02T22:25:00", "legKey": "16108879:181016:6/2/2025 10:25:00 PM", "customerKey": "878F52049FC2E66EFCC1419F9E722E6433D826699A166C0F054C3015B09CE819"}, {"PFID": 181192, "depDate": "2025-06-03T01:55:00", "legKey": "16108879:181192:6/3/2025 1:55:00 AM", "customerKey": "E342DFE9C57BCCA4A68C1AD2A12A14A3CFE50E1874DA10E689AC4DE262A50BB6"}], "active": true}, {"segKey": "16108879:16108879:6/6/2025 10:25:00 PM", "LFID": 16108879, "depDate": "2025-06-06T00:00:00", "flightGroupId": "16108879", "org": "DOH", "dest": "CMB", "depTime": "2025-06-06T22:25:00", "depTimeGMT": "2025-06-06T19:25:00", "arrTime": "2025-06-07T08:10:00", "operCarrier": "FZ", "operFlightNum": "020/579", "mrktCarrier": "FZ ", "mrktFlightNum": "020/579", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181016, "depDate": "2025-06-06T22:25:00", "legKey": "16108879:181016:6/6/2025 10:25:00 PM", "customerKey": "7FBAA274F5B16EF8B87C9B0993F6C7528E6471A74817439685F8174F1CA1CBFD"}, {"PFID": 181192, "depDate": "2025-06-07T01:55:00", "legKey": "16108879:181192:6/7/2025 1:55:00 AM", "customerKey": "C90BA858A06B3831D7D9993F348ACC0B8E2BD55AEA070F24989E84C6F3A90538"}], "active": true}], "persons": [{"paxID": 266030372, "fName": "CASSIM SAYIB", "lName": "MOHAMED SUHAIL", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1993-10-26T00:00:00", "nationality": "144", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "4/16/2025 7:04:56 PM", "provider": "AIG", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "UOL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "986432252", "insuTransID": "00644501-02b0-4d28-bc03-7b69c4b9d515", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67fffc5c0007780000001c2b#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-16T19:09:04"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "status": 1, "fareClass": "R", "operFareClass": "R", "FBC": "ROL7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "2K6C4-<PERSON><PERSON><PERSON><PERSON>-INS/07cd814e-b693-4f35-8eb7-31a962135710", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6821c25a0007780000000fbb#266030372#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "bookDate": "2025-05-12T09:51:43"}]}], "payments": [{"paymentID": *********, "paxID": 268691186, "method": "MSCD", "status": "1", "paidDate": "2025-05-12T10:34:55", "cardNum": "************1011", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 406.22, "baseCurr": "QAR", "baseAmt": 406.22, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON> <PERSON><PERSON>", "authCode": "176496", "reference": "22988247", "externalReference": "22988247", "tranId": "21357522", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21357522}, {"paymentID": *********, "paxID": 266030703, "method": "MSCD", "status": "1", "paidDate": "2025-04-16T19:13:15", "cardNum": "************1011", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 930.09, "baseCurr": "QAR", "baseAmt": 930.09, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON> <PERSON><PERSON>", "authCode": "271725", "reference": "22457314", "externalReference": "22457314", "tranId": "20835001", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20835001}, {"paymentID": 205996153, "paxID": 266030699, "method": "MSCD", "status": "2", "paidDate": "2025-04-16T19:10:25", "cardNum": "************1011", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 930.09, "baseCurr": "QAR", "baseAmt": 930.09, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON> <PERSON><PERSON>", "reference": "22457280", "externalReference": "22457280", "tranId": "20835001", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20835001}], "OAFlights": null, "physicalFlights": [{"key": "16108879:181016:2025-06-02T10:25:00 PM", "LFID": 16108879, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-06-02T22:25:00", "depTime": "2025-06-02T22:25:00", "arrTime": "2025-06-03T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "16108879:181192:2025-06-03T01:55:00 AM", "LFID": 16108879, "PFID": 181192, "org": "DXB", "dest": "CMB", "depDate": "2025-06-03T01:55:00", "depTime": "2025-06-03T01:55:00", "arrTime": "2025-06-03T08:10:00", "carrier": "FZ", "flightNum": "579", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "579", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "CMB", "operatingCarrier": "FZ", "flightDuration": 17100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Colombo", "isActive": true}, {"key": "16108879:181016:2025-06-06T10:25:00 PM", "LFID": 16108879, "PFID": 181016, "org": "DOH", "dest": "DXB", "depDate": "2025-06-06T22:25:00", "depTime": "2025-06-06T22:25:00", "arrTime": "2025-06-07T00:40:00", "carrier": "FZ", "flightNum": "020", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "020", "flightStatus": "OPEN", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "16108879:181192:2025-06-07T01:55:00 AM", "LFID": 16108879, "PFID": 181192, "org": "DXB", "dest": "CMB", "depDate": "2025-06-07T01:55:00", "depTime": "2025-06-07T01:55:00", "arrTime": "2025-06-07T08:10:00", "carrier": "FZ", "flightNum": "579", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "579", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "CMB", "operatingCarrier": "FZ", "flightDuration": 17100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Colombo", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-16T19:09:04", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-16T19:09:03"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353033, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1342351666, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353032, "paymentMap": [{"key": "1342351666:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1342351735, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353029, "paymentMap": [{"key": "1342351735:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1342351662, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353031, "paymentMap": [{"key": "1342351662:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1342351659, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353030, "paymentMap": [{"key": "1342351659:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1342351665, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353034, "paymentMap": [{"key": "1342351665:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1308353033, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353033:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1308353031, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353031:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1308353032, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353032:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1308353034, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353034:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1308353030, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353030:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1308353029, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353029:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342351664, "codeType": "AIR", "amt": -620, "curr": "QAR", "originalAmt": -620, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-12T09:51:44", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1342351664:*********", "paymentID": *********, "amt": -620, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 620, "curr": "QAR", "originalAmt": 620, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-16T19:09:04", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 620, "approveCode": 0}]}, {"chargeID": 1308359868, "codeType": "PMNT", "amt": 27.09, "curr": "QAR", "originalAmt": 27.09, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-16T19:13:23", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308359868:*********", "paymentID": *********, "amt": 27.09, "approveCode": 0}]}, {"chargeID": 1342351736, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-12T09:51:44", "desc": "Penalty AddedDueToModify FZ  020 DOH  - DXB  06-Jun-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351736:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1308353036, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342351660, "codeType": "BUPZ", "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "BUPZ", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1308353040, "paymentMap": [{"key": "1342351660:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1308353040, "codeType": "BUPZ", "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "BUPZ", "comment": "FLXID:GCC-AE DOH-PK/NP/HBE/KRT/SPX/MBA/MGQ/KBL/CMB/CGP/DAC/KTM:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1308353040:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1308353042, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181192"}, {"chargeID": 1308353041, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-16T19:09:04", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}]}, {"recNum": 2, "charges": [{"chargeID": 1342351843, "codeType": "INSU", "amt": 35.39, "curr": "QAR", "originalAmt": 35.39, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-12T09:51:44", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351843:*********", "paymentID": *********, "amt": 35.39, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-12T09:51:44"}, {"chargeID": 1342351738, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1342351737, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351738:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342351740, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1342351737, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351740:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1342351741, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1342351737, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351741:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1342351742, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1342351737, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351742:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1342351744, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1342351737, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351744:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1342351743, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1342351737, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351743:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1342351739, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1342351737, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351739:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1342351737, "codeType": "AIR", "amt": 830, "curr": "QAR", "originalAmt": 830, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-12T09:51:44", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351737:*********", "paymentID": *********, "amt": 349, "approveCode": 0}, {"key": "1342351737:*********", "paymentID": *********, "amt": 481, "approveCode": 0}]}, {"chargeID": 1342458155, "codeType": "PMNT", "amt": 11.83, "curr": "QAR", "originalAmt": 11.83, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-12T10:35:00", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342458155:*********", "paymentID": *********, "amt": 11.83, "approveCode": 0}]}, {"chargeID": 1342351745, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1342351737, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:44", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1342351844, "codeType": "BUPZ", "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-12T09:51:45", "desc": "BUPZ", "comment": "FLXID:GCC-AE DOH-PK/NP/HBE/KRT/SPX/MBA/MGQ/KBL/CMB/CGP/DAC/KTM:", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1342351844:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1342351833, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:45", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181192"}, {"chargeID": 1342351832, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-12T09:51:45", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181016"}]}], "parentPNRs": [], "childPNRs": []}