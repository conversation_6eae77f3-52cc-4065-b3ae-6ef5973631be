# Example Usage

This document shows how to use the Cover Genius Policy Analysis Application.

## Prerequisites

1. **Environment Setup**: Make sure you have created a `.env` file with your API credentials:
   ```bash
   cp .env.example .env
   # Edit .env with your actual credentials
   ```

2. **Dependencies**: Install all required packages:
   ```bash
   npm install
   ```

## Running the Application

### Basic Usage

```bash
node main.js ABC123
```

### Expected Output

```
============================================================
Cover Genius Policy Analysis - Starting Process
============================================================
Processing PNR: ABC123

Step 1: Retrieving PNR data from FlyDubai API...
Security token obtained successfully
PNR data fetched successfully
Saved PNR data to RetrievePNR-ABC123.json
✓ PNR data retrieved successfully

Step 2: Extracting Cover Genius policy IDs...
Found Cover Genius policy ID: RJ6U4-VFXUS-INS
✓ Found 1 policy ID(s): RJ6U4-VFXUS-INS

Step 3.1: Processing policy ID: RJ6U4-VFXUS-INS
  - Fetching policy details from Cover Genius API...
  ✓ Policy data retrieved successfully
  - Processing insurance data...
  ✓ Processed 3 insurance records
  - Generating HTML report...

HTML report generated: insurance-report-ABC123-RJ6U4-VFXUS-INS.html
  ✓ Report generated for policy RJ6U4-VFXUS-INS

============================================================
Process completed successfully!
============================================================
```

## Generated Files

After running the application, you'll find these files in your directory:

1. **`RetrievePNR-ABC123.json`** - Raw PNR data from FlyDubai API
2. **`insurance-report-ABC123-RJ6U4-VFXUS-INS.html`** - Comprehensive HTML report

## Error Scenarios

### Missing Environment Variables
```bash
$ node main.js ABC123
❌ Fatal error in main process: Missing required FlyDubai API environment variables. Please check your .env file.
```

### No Policy IDs Found
```bash
$ node main.js XYZ789
============================================================
Cover Genius Policy Analysis - Starting Process
============================================================
Processing PNR: XYZ789

Step 1: Retrieving PNR data from FlyDubai API...
✓ PNR data retrieved successfully

Step 2: Extracting Cover Genius policy IDs...
❌ No Cover Genius policy IDs found in PNR data
Process completed - no policies to analyze
```

### Invalid PNR Format
```bash
$ node main.js AB
Error: PNR number should be between 3 and 10 characters
```

## Alternative Usage with npm

You can also use the npm script:

```bash
npm start ABC123
```

## Programmatic Usage

You can also use the modules programmatically in your own code:

```javascript
const { main, processInsuranceData } = require('./main');

// Run the complete workflow
main('ABC123').then(() => {
    console.log('Analysis complete!');
}).catch(error => {
    console.error('Error:', error.message);
});
```
