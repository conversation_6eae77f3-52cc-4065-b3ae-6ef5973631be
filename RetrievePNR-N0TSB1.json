{"seriesNum": "299", "PNR": "N0TSB1", "bookAgent": "WEB_MOBILE", "resCurrency": "USD", "PNRPin": "82680384", "bookDate": "2025-05-09T11:43:03", "modifyDate": "2025-05-18T17:16:11", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "464dd21f9b5a94oai4fb28u8k0s06842m34206836d77", "securityGUID": "464dd21f9b5a94oai4fb28u8k0s06842m34206836d77", "lastLoadGUID": "356ABE3DC608538AE0631F206F0A2F75", "isAsyncPNR": false, "MasterPNR": "N0TSB1", "segments": [{"segKey": "16087477:16087477:5/12/2025 2:10:00 AM", "LFID": 16087477, "depDate": "2025-05-12T00:00:00", "flightGroupId": "16087477", "org": "IST", "dest": "DXB", "depTime": "2025-05-12T02:10:00", "depTimeGMT": "2025-05-11T23:10:00", "arrTime": "2025-05-12T07:40:00", "operCarrier": "FZ", "operFlightNum": "756", "mrktCarrier": "FZ ", "mrktFlightNum": "756", "persons": [{"recNum": 2, "status": 5}, {"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181238, "depDate": "2025-05-12T02:10:00", "legKey": "16087477:181238:5/12/2025 2:10:00 AM", "customerKey": "2239E93E7DAD713C38E93388689EB96E29E772130056D438E7471D4F1AF4C586"}], "active": true}, {"segKey": "16087544:16087544:5/22/2025 5:30:00 PM", "LFID": 16087544, "depDate": "2025-05-22T00:00:00", "flightGroupId": "16087544", "org": "DXB", "dest": "SAW", "depTime": "2025-05-22T17:30:00", "depTimeGMT": "2025-05-22T13:30:00", "arrTime": "2025-05-22T21:15:00", "operCarrier": "FZ", "operFlightNum": "753", "mrktCarrier": "FZ ", "mrktFlightNum": "753", "persons": [{"recNum": 4, "status": 0}, {"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181295, "depDate": "2025-05-22T17:30:00", "legKey": "16087544:181295:5/22/2025 5:30:00 PM", "customerKey": "CEAFA737D314C72409097D5C3C0FCF7D7EE46D082CCDC372AD9B9AFBD08FBED5"}], "active": true, "changeType": "AC"}, {"segKey": "16087490:16087490:5/19/2025 9:15:00 AM", "LFID": 16087490, "depDate": "2025-05-19T00:00:00", "flightGroupId": "16087490", "org": "DXB", "dest": "SAW", "depTime": "2025-05-19T09:15:00", "depTimeGMT": "2025-05-19T05:15:00", "arrTime": "2025-05-19T13:05:00", "operCarrier": "FZ", "operFlightNum": "751", "mrktCarrier": "FZ ", "mrktFlightNum": "751", "persons": [{"recNum": 5, "status": 5}, {"recNum": 6, "status": 5}], "legDetails": [{"PFID": 181221, "depDate": "2025-05-19T09:15:00", "legKey": "16087490:181221:5/19/2025 9:15:00 AM", "customerKey": "B3A81CF8C825B221146FBFE34AFB13C1093DA47458A3E6F5294DCCD8BF1F7B74"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 268411952, "fName": "SELAMI", "lName": "TUFEKCI", "title": "MR", "PTCID": 1, "gender": "M", "recNum": [2, 4, 5]}, {"paxID": 268411953, "fName": "PATRICIA ALEJANDRA", "lName": "LALLANES", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 3, 6]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7TR5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681de8c00007780000004b1b#2#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-09T11:43:03"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BRB7TR5", "fareBrand": "Value", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681de8c00007780000004b1b#1#1#MOBILE#INTERNAL#CREATE", "fareTypeID": 22, "channelID": 12, "bookDate": "2025-05-09T11:43:03"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "U", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8TR5", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 6, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "681de8c00007780000004b1b#2#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-09T11:43:03"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "U", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "URL8TR5", "fareBrand": "Flex", "cabin": "ECONOMY", "changeConsent": 0, "toRecNum": 5, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681de8c00007780000004b1b#1#2#MOBILE#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-09T11:43:03"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/17/2025 9:06:22 AM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URL8TR5", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "ZLRDE-SU723-INS/6417af9a-6065-4718-933b-6c6dd9c48d53", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682850840007770000008aac#268411952#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-17T09:06:20"}]}, {"recNum": 6, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/17/2025 9:06:22 AM", "provider": "<PERSON>", "status": 5, "fareClass": "U", "operFareClass": "U", "FBC": "URL8TR5", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "ZLRDE-SU723-INS/6417af9a-6065-4718-933b-6c6dd9c48d53", "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "682850840007770000008aac#268411953#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-17T09:06:20"}]}], "payments": [{"paymentID": *********, "paxID": 269332294, "method": "IPAY", "status": "1", "paidDate": "2025-05-18T07:17:00", "cardNum": "************3526", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 191.58, "baseCurr": "USD", "baseAmt": 191.58, "userID": "olci", "channelID": 20, "cardHolderName": "se<PERSON>i t�<PERSON>ci", "authCode": "470016", "reference": "23101682", "externalReference": "23101682", "tranId": "21475379", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21475379}, {"paymentID": *********, "paxID": 268412598, "method": "IPAY", "status": "1", "paidDate": "2025-05-09T11:48:48", "cardNum": "************5950", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 1052.41, "baseCurr": "USD", "baseAmt": 1052.41, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "se<PERSON>i t�<PERSON>ci", "authCode": "102244", "reference": "22931971", "externalReference": "22931971", "tranId": "21304843", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21304843}, {"paymentID": *********, "paxID": 268411953, "method": "VCHR", "status": "1", "paidDate": "2025-05-17T09:06:19", "voucherNum": 3261350, "paidCurr": "USD", "paidAmt": -17.87, "baseCurr": "USD", "baseAmt": -17.87, "userID": "MOBILE_APP", "channelID": 12, "voucherNumFull": "WBXBGK", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 268411952, "method": "VCHR", "status": "1", "paidDate": "2025-05-17T09:06:19", "voucherNum": 3261349, "paidCurr": "USD", "paidAmt": -23.57, "baseCurr": "USD", "baseAmt": -23.57, "userID": "MOBILE_APP", "channelID": 12, "voucherNumFull": "RWZ1C9", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": 208443751, "paxID": 268412640, "method": "MSCD", "status": "2", "paidDate": "2025-05-09T11:46:36", "cardNum": "************3012", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 1052.41, "baseCurr": "USD", "baseAmt": 1052.41, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "Selami TUFEKCI", "reference": "22931927", "externalReference": "22931927", "tranId": "21304843", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21304843}, {"paymentID": 208443753, "paxID": 268412622, "method": "MSCD", "status": "2", "paidDate": "2025-05-09T11:43:30", "cardNum": "************3012", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 1052.41, "baseCurr": "USD", "baseAmt": 1052.41, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "Selami TUFEKCI", "reference": "22931903", "externalReference": "22931903", "tranId": "21304843", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21304843}, {"paymentID": 208443722, "paxID": 268412593, "method": "IPAY", "status": "2", "paidDate": "2025-05-09T11:47:01", "cardNum": "************5950", "gateway": "EPS", "paidCurr": "USD", "paidAmt": 1052.41, "baseCurr": "USD", "baseAmt": 1052.41, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "se<PERSON>i t�<PERSON>ci", "reference": "22931948", "externalReference": "22931948", "tranId": "21304843", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgtrecomusdallniall001", "exchangeRate": "1", "resExternalPaymentID": 21304843}], "OAFlights": null, "physicalFlights": [{"key": "16087477:181238:2025-05-12T02:10:00 AM", "LFID": 16087477, "PFID": 181238, "org": "IST", "dest": "DXB", "depDate": "2025-05-12T02:10:00", "depTime": "2025-05-12T02:10:00", "arrTime": "2025-05-12T07:40:00", "carrier": "FZ", "flightNum": "756", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "756", "flightStatus": "CLOSED", "originMetroGroup": "IST", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16200, "reaccomChangeAlert": false, "originName": "Istanbul Airport", "destinationName": "Dubai International Airport", "isActive": false, "flightChangeTime": "5/12/2025 9:25:15 AM"}, {"key": "16087490:181221:2025-05-19T09:15:00 AM", "LFID": 16087490, "PFID": 181221, "org": "DXB", "dest": "SAW", "depDate": "2025-05-19T09:15:00", "depTime": "2025-05-19T09:15:00", "arrTime": "2025-05-19T13:05:00", "carrier": "FZ", "flightNum": "751", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "751", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Sabiha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:57:08 AM"}, {"key": "16087544:181295:2025-05-22T05:30:00 PM", "LFID": 16087544, "PFID": 181295, "org": "DXB", "dest": "SAW", "depDate": "2025-05-22T17:30:00", "depTime": "2025-05-22T17:30:00", "arrTime": "2025-05-22T21:15:00", "carrier": "FZ", "flightNum": "753", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "753", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "IST", "operatingCarrier": "FZ", "flightDuration": 17100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Istanbul Sabiha", "isActive": false, "changeType": "AC", "flightChangeTime": "3/5/2025 11:55:28 AM"}], "chargeInfos": [{"recNum": 5, "charges": [{"chargeID": 1349968507, "codeType": "INSU", "amt": 9.72, "curr": "USD", "originalAmt": 9.72, "originalCurr": "USD", "status": 1, "exchRate": 1, "billDate": "2025-05-17T09:06:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349968507:*********", "paymentID": *********, "amt": 9.72, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.0\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-17T09:06:22"}, {"chargeID": 1349964443, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1349964441, "amt": 20.4, "curr": "USD", "originalAmt": 20.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964443:*********", "paymentID": *********, "amt": 20.4, "approveCode": 0}]}, {"chargeID": 1349964444, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349964441, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964444:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1349964442, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1349964441, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964442:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1349964446, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1349964441, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964446:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1349964445, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349964441, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964445:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}]}, {"chargeID": 1349964441, "codeType": "AIR", "amt": 114.41, "curr": "USD", "originalAmt": 114.41, "originalCurr": "USD", "status": 1, "billDate": "2025-05-17T09:06:22", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964441:*********", "paymentID": *********, "amt": 114.41, "approveCode": 0}]}, {"chargeID": 1350910056, "codeType": "PMNT", "amt": 5.58, "curr": "USD", "originalAmt": 5.58, "originalCurr": "USD", "status": 1, "billDate": "2025-05-18T07:17:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350910056:*********", "paymentID": *********, "amt": 5.58, "approveCode": 0}]}, {"chargeID": 1349968504, "codeType": "FRST", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 1, "billDate": "2025-05-17T09:06:22", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181221"}, {"chargeID": 1349964448, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1349964441, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349964447, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349964441, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1350909344, "codeType": "BUPZ", "amt": 93, "curr": "USD", "originalAmt": 93, "originalCurr": "USD", "status": 1, "billDate": "2025-05-18T07:16:27", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350909344:*********", "paymentID": *********, "amt": 93, "approveCode": 0}], "ssrCommentId": "*********"}, {"chargeID": 1349964467, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181221"}]}, {"recNum": 2, "charges": [{"chargeID": 1338804204, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804199, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804204:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-09T11:43:02"}, {"chargeID": 1338804202, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1338804199, "amt": 22.7, "curr": "USD", "originalAmt": 22.7, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804202:*********", "paymentID": *********, "amt": 22.7, "approveCode": 0}]}, {"chargeID": 1338804201, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804199, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804201:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1338804203, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1338804199, "amt": 3.4, "curr": "USD", "originalAmt": 3.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804203:*********", "paymentID": *********, "amt": 3.4, "approveCode": 0}]}, {"chargeID": 1338804199, "codeType": "AIR", "amt": 87.5, "curr": "USD", "originalAmt": 87.5, "originalCurr": "USD", "status": 1, "billDate": "2025-05-09T11:43:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804199:*********", "paymentID": *********, "amt": 87.5, "approveCode": 0}]}, {"chargeID": 1338814537, "codeType": "PMNT", "amt": 30.65, "curr": "USD", "originalAmt": 30.65, "originalCurr": "USD", "status": 1, "billDate": "2025-05-09T11:48:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338814537:*********", "paymentID": *********, "amt": 30.65, "approveCode": 0}]}, {"chargeID": 1338804232, "codeType": "XLGR", "amt": 42.5, "curr": "USD", "originalAmt": 42.5, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181238", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804232:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}], "PFID": "181238"}, {"chargeID": 1338804200, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338804199, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804239, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181238"}]}, {"recNum": 4, "charges": [{"chargeID": 1349964435, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338804206, "amt": -20.4, "curr": "USD", "originalAmt": -20.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804210, "paymentMap": [{"key": "1349964435:*********", "paymentID": *********, "amt": -20.4, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-17T09:06:21"}, {"chargeID": 1349964436, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804206, "amt": -1.4, "curr": "USD", "originalAmt": -1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804207, "paymentMap": [{"key": "1349964436:*********", "paymentID": *********, "amt": -1.4, "approveCode": 0}]}, {"chargeID": 1349964439, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804206, "amt": -85.09, "curr": "USD", "originalAmt": -85.09, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804209, "paymentMap": [{"key": "1349964439:*********", "paymentID": *********, "amt": -85.09, "approveCode": 0}]}, {"chargeID": 1349964440, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338804206, "amt": -12.3, "curr": "USD", "originalAmt": -12.3, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804211, "paymentMap": [{"key": "1349964440:*********", "paymentID": *********, "amt": -12.3, "approveCode": 0}]}, {"chargeID": 1349964437, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338804206, "amt": -1.4, "curr": "USD", "originalAmt": -1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804212, "paymentMap": [{"key": "1349964437:*********", "paymentID": *********, "amt": -1.4, "approveCode": 0}]}, {"chargeID": 1338804210, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338804206, "amt": 20.4, "curr": "USD", "originalAmt": 20.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804210:*********", "paymentID": *********, "amt": 20.4, "approveCode": 0}]}, {"chargeID": 1338804207, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804206, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804207:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1338804211, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338804206, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804211:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1338804212, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338804206, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804212:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1338804209, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804206, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804209:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}]}, {"chargeID": 1349964434, "codeType": "AIR", "amt": -99.5, "curr": "USD", "originalAmt": -99.5, "originalCurr": "USD", "status": 0, "billDate": "2025-05-17T09:06:21", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804206, "paymentMap": [{"key": "1349964434:*********", "paymentID": *********, "amt": -23.57, "approveCode": 0}, {"key": "1349964434:*********", "paymentID": *********, "amt": -75.93, "approveCode": 0}]}, {"chargeID": 1338804206, "codeType": "AIR", "amt": 99.5, "curr": "USD", "originalAmt": 99.5, "originalCurr": "USD", "status": 0, "billDate": "2025-05-09T11:43:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804206:*********", "paymentID": *********, "amt": 99.5, "approveCode": 0}]}, {"chargeID": 1349964438, "codeType": "XLGR", "amt": -48.2, "curr": "USD", "originalAmt": -48.2, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804234, "paymentMap": [{"key": "1349964438:*********", "paymentID": *********, "amt": -48.2, "approveCode": 0}], "PFID": "181295"}, {"chargeID": 1338804234, "codeType": "XLGR", "amt": 48.2, "curr": "USD", "originalAmt": 48.2, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181295", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804234:*********", "paymentID": *********, "amt": 48.2, "approveCode": 0}], "PFID": "181295"}, {"chargeID": 1338804213, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1338804206, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804208, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1338804206, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804240, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181295"}]}, {"recNum": 6, "charges": [{"chargeID": 1349968506, "codeType": "INSU", "amt": 9.72, "curr": "USD", "originalAmt": 9.72, "originalCurr": "USD", "status": 1, "exchRate": 1, "billDate": "2025-05-17T09:06:22", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349968506:*********", "paymentID": *********, "amt": 9.72, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.0\",\"Premium\":\"19.44\",\"Tax\":\"0.93\",\"SegPaxCount\":\"2\"}", "ChargeBookDate": "2025-05-17T09:06:22"}, {"chargeID": 1349964456, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1349964454, "amt": 20.4, "curr": "USD", "originalAmt": 20.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964456:*********", "paymentID": *********, "amt": 20.4, "approveCode": 0}]}, {"chargeID": 1349964458, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1349964454, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964458:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}]}, {"chargeID": 1349964459, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1349964454, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964459:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1349964457, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1349964454, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964457:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1349964455, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1349964454, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964455:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1349964454, "codeType": "AIR", "amt": 114.41, "curr": "USD", "originalAmt": 114.41, "originalCurr": "USD", "status": 1, "billDate": "2025-05-17T09:06:22", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1349964454:*********", "paymentID": *********, "amt": 114.41, "approveCode": 0}]}, {"chargeID": 1349968505, "codeType": "FRST", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 1, "billDate": "2025-05-17T09:06:22", "desc": "FRST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181221", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181221"}, {"chargeID": 1349964461, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1349964454, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1349964460, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1349964454, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1350909343, "codeType": "BUPZ", "amt": 93, "curr": "USD", "originalAmt": 93, "originalCurr": "USD", "status": 1, "billDate": "2025-05-18T07:16:27", "reasonID": 12, "channelID": 20, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350909343:*********", "paymentID": *********, "amt": 93, "approveCode": 0}], "ssrCommentId": "*********"}, {"chargeID": 1349964468, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-17T09:06:22", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181221"}]}, {"recNum": 1, "charges": [{"chargeID": 1338804218, "codeType": "TAX", "taxID": 13730, "taxCode": "TR", "taxChargeID": 1338804215, "amt": 22.7, "curr": "USD", "originalAmt": 22.7, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Airport Service Charge (International)", "comment": "Airport Service Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804218:*********", "paymentID": *********, "amt": 22.7, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-09T11:43:02"}, {"chargeID": 1338804220, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804215, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804220:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}]}, {"chargeID": 1338804217, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804215, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804217:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1338804219, "codeType": "TAX", "taxID": 13731, "taxCode": "M6", "taxChargeID": 1338804215, "amt": 3.4, "curr": "USD", "originalAmt": 3.4, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "International Flights Security Charge", "comment": "International Flights Security Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804219:*********", "paymentID": *********, "amt": 3.4, "approveCode": 0}]}, {"chargeID": 1338804215, "codeType": "AIR", "amt": 87.5, "curr": "USD", "originalAmt": 87.5, "originalCurr": "USD", "status": 1, "billDate": "2025-05-09T11:43:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804215:*********", "paymentID": *********, "amt": 87.5, "approveCode": 0}]}, {"chargeID": 1338804236, "codeType": "XLGR", "amt": 48.2, "curr": "USD", "originalAmt": 48.2, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181238", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804236:*********", "paymentID": *********, "amt": 48.2, "approveCode": 0}], "PFID": "181238"}, {"chargeID": 1338804216, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1338804215, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804241, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 1, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181238"}]}, {"recNum": 3, "charges": [{"chargeID": 1349964430, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804222, "amt": -85.09, "curr": "USD", "originalAmt": -85.09, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804225, "paymentMap": [{"key": "1349964430:*********", "paymentID": *********, "amt": -85.09, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-17T09:06:21"}, {"chargeID": 1349964407, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338804222, "amt": -1.4, "curr": "USD", "originalAmt": -1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804228, "paymentMap": [{"key": "1349964407:*********", "paymentID": *********, "amt": -1.4, "approveCode": 0}]}, {"chargeID": 1349964433, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804222, "amt": -1.4, "curr": "USD", "originalAmt": -1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804223, "paymentMap": [{"key": "1349964433:*********", "paymentID": *********, "amt": -1.4, "approveCode": 0}]}, {"chargeID": 1349964408, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338804222, "amt": -20.4, "curr": "USD", "originalAmt": -20.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804226, "paymentMap": [{"key": "1349964408:*********", "paymentID": *********, "amt": -20.4, "approveCode": 0}]}, {"chargeID": 1349964429, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338804222, "amt": -12.3, "curr": "USD", "originalAmt": -12.3, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804227, "paymentMap": [{"key": "1349964429:*********", "paymentID": *********, "amt": -12.3, "approveCode": 0}]}, {"chargeID": 1338804228, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1338804222, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804228:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1338804226, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1338804222, "amt": 20.4, "curr": "USD", "originalAmt": 20.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804226:*********", "paymentID": *********, "amt": 20.4, "approveCode": 0}]}, {"chargeID": 1338804227, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1338804222, "amt": 12.3, "curr": "USD", "originalAmt": 12.3, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804227:*********", "paymentID": *********, "amt": 12.3, "approveCode": 0}]}, {"chargeID": 1338804225, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1338804222, "amt": 85.09, "curr": "USD", "originalAmt": 85.09, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804225:*********", "paymentID": *********, "amt": 85.09, "approveCode": 0}]}, {"chargeID": 1338804223, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1338804222, "amt": 1.4, "curr": "USD", "originalAmt": 1.4, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804223:*********", "paymentID": *********, "amt": 1.4, "approveCode": 0}]}, {"chargeID": 1349964431, "codeType": "AIR", "amt": -99.5, "curr": "USD", "originalAmt": -99.5, "originalCurr": "USD", "status": 0, "billDate": "2025-05-17T09:06:21", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804222, "paymentMap": [{"key": "1349964431:*********", "paymentID": *********, "amt": -81.63, "approveCode": 0}, {"key": "1349964431:*********", "paymentID": *********, "amt": -17.87, "approveCode": 0}]}, {"chargeID": 1338804222, "codeType": "AIR", "amt": 99.5, "curr": "USD", "originalAmt": 99.5, "originalCurr": "USD", "status": 0, "billDate": "2025-05-09T11:43:03", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804222:*********", "paymentID": *********, "amt": 99.5, "approveCode": 0}]}, {"chargeID": 1349964432, "codeType": "XLGR", "amt": -42.5, "curr": "USD", "originalAmt": -42.5, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-17T09:06:21", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1338804238, "paymentMap": [{"key": "1349964432:*********", "paymentID": *********, "amt": -42.5, "approveCode": 0}], "PFID": "181295"}, {"chargeID": 1338804238, "codeType": "XLGR", "amt": 42.5, "curr": "USD", "originalAmt": 42.5, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "XLGR", "comment": "FLXID:XLGR_EMER_ZONE3_MID::181295", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1338804238:*********", "paymentID": *********, "amt": 42.5, "approveCode": 0}], "PFID": "181295"}, {"chargeID": 1338804229, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1338804222, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804224, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1338804222, "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1338804242, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "USD", "originalAmt": 0, "originalCurr": "USD", "status": 0, "exchRate": 0, "billDate": "2025-05-09T11:43:03", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181295"}]}], "parentPNRs": [], "childPNRs": []}