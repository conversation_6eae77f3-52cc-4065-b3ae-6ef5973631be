{"seriesNum": "299", "PNR": "QTBLCQ", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "79977957", "bookDate": "2025-01-29T08:41:19", "modifyDate": "2025-05-15T05:28:43", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 1, "webBookingID": "e635kef3h60du0h66462wb34c8379c28b9b8997df410", "securityGUID": "e635kef3h60du0h66462wb34c8379c28b9b8997df410", "lastLoadGUID": "59c0952a-9011-4b95-962d-a6817ebf7fe0", "isAsyncPNR": false, "MasterPNR": "QTBLCQ", "segments": [{"segKey": "16087759:16087759:5/30/2025 9:05:00 AM", "LFID": 16087759, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087759", "org": "DXB", "dest": "BEG", "depTime": "2025-05-30T09:05:00", "depTimeGMT": "2025-05-30T05:05:00", "arrTime": "2025-05-30T12:55:00", "operCarrier": "FZ", "operFlightNum": "1745", "mrktCarrier": "FZ ", "mrktFlightNum": "1745", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181549, "depDate": "2025-05-30T09:05:00", "legKey": "16087759:181549:5/30/2025 9:05:00 AM", "customerKey": "AEC5E92FB2711C8FC05A1E4775BF4D97E5758ADE014EF14383788CEA7513108C"}], "active": true, "changeType": "TK"}, {"segKey": "16087827:16087827:4/3/2025 6:55:00 PM", "LFID": 16087827, "depDate": "2025-04-03T00:00:00", "flightGroupId": "16087827", "org": "DXB", "dest": "BEG", "depTime": "2025-04-03T18:55:00", "depTimeGMT": "2025-04-03T14:55:00", "arrTime": "2025-04-03T22:45:00", "operCarrier": "FZ", "operFlightNum": "1749", "mrktCarrier": "FZ ", "mrktFlightNum": "1749", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181557, "depDate": "2025-04-03T18:55:00", "legKey": "16087827:181557:4/3/2025 6:55:00 PM", "customerKey": "77849160B19BF7D71D47E6526B970AE910D31102878B5626C414FEFC3DA93E5A"}], "active": true, "changeType": "TK"}, {"segKey": "16087759:16087759:6/3/2025 9:05:00 AM", "LFID": 16087759, "depDate": "2025-06-03T00:00:00", "flightGroupId": "16087759", "org": "DXB", "dest": "BEG", "depTime": "2025-06-03T09:05:00", "depTimeGMT": "2025-06-03T05:05:00", "arrTime": "2025-06-03T12:55:00", "operCarrier": "FZ", "operFlightNum": "1745", "mrktCarrier": "FZ ", "mrktFlightNum": "1745", "persons": [{"recNum": 3, "status": 1}], "legDetails": [{"PFID": 181549, "depDate": "2025-06-03T09:05:00", "legKey": "16087759:181549:6/3/2025 9:05:00 AM", "customerKey": "A06CF8D8C4FAAB58AF239D1A77B33AD43E3FEB1A8116FB15B8BF6922DBC6E8A3"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 257869812, "fName": "MAJA", "lName": "NOVKOVIC", "title": "MS", "PTCID": 1, "gender": "F", "recNum": [1, 2, 3]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "U", "status": 0, "fareClass": "U", "operFareClass": "U", "FBC": "UOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6799e7790007770000012597#1#1#WEB#INTERNAL#CREATE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-01-29T08:41:19"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "R", "insuPurchasedate": "3/27/2025 6:14:22 AM", "provider": "AIG", "status": 0, "fareClass": "R", "operFareClass": "R", "FBC": "ROL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuConfNum": "985518656", "insuTransID": "6d66f004-f339-472e-8346-75a04b902a0c", "toRecNum": 3, "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "67e4ecb7000778000000385f#257869812#1#WEB#SFQE#CHANGE", "fareTypeID": 23, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-03-27T06:19:13"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "U", "insuPurchasedate": "5/15/2025 5:27:53 AM", "provider": "<PERSON>", "status": 1, "fareClass": "U", "operFareClass": "U", "FBC": "UOL8AE2", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "VKGVG-<PERSON><PERSON><PERSON><PERSON>-INS/62f97f9c-f850-4d91-8216-db06a64fca97", "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68257a7900077800000066c4#257869812#1#WEB#SFQE#CHANGE", "fareTypeID": 23, "channelID": 2, "bookDate": "2025-05-15T05:27:52"}]}], "payments": [{"paymentID": *********, "paxID": 269022523, "method": "MSCD", "status": "1", "paidDate": "2025-05-15T05:28:38", "cardNum": "************9734", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 47.07, "baseCurr": "AED", "baseAmt": 47.07, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "752981", "reference": "23041608", "externalReference": "23041608", "tranId": "21416515", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21416515}, {"paymentID": *********, "paxID": 263852708, "method": "VISA", "status": "1", "paidDate": "2025-03-27T06:19:43", "cardNum": "************6671", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 375.59, "baseCurr": "AED", "baseAmt": 375.59, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON><PERSON><PERSON>", "authCode": "138642", "reference": "22019309", "externalReference": "22019309", "tranId": "20413631", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 20413631}, {"paymentID": *********, "paxID": 257869805, "method": "VISA", "status": "1", "paidDate": "2025-01-29T08:41:25", "cardNum": "************7247", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1531.61, "baseCurr": "AED", "baseAmt": 1531.61, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "502331", "reference": "20974358", "externalReference": "20974358", "tranId": "19309428", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallecomaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 19309428}], "OAFlights": null, "physicalFlights": [{"key": "16087827:181557:2025-04-03T06:55:00 PM", "LFID": 16087827, "PFID": 181557, "org": "DXB", "dest": "BEG", "depDate": "2025-04-03T18:55:00", "depTime": "2025-04-03T18:55:00", "arrTime": "2025-04-03T22:45:00", "carrier": "FZ", "flightNum": "1749", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1749", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": false, "changeType": "TK", "flightChangeTime": "2/7/2025 8:12:46 AM"}, {"key": "16087759:181549:2025-05-30T09:05:00 AM", "LFID": 16087759, "PFID": 181549, "org": "DXB", "dest": "BEG", "depDate": "2025-05-30T09:05:00", "depTime": "2025-05-30T09:05:00", "arrTime": "2025-05-30T12:55:00", "carrier": "FZ", "flightNum": "1745", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1745", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 8:05:34 AM"}, {"key": "16087759:181549:2025-06-03T09:05:00 AM", "LFID": 16087759, "PFID": 181549, "org": "DXB", "dest": "BEG", "depDate": "2025-06-03T09:05:00", "depTime": "2025-06-03T09:05:00", "arrTime": "2025-06-03T12:55:00", "carrier": "FZ", "flightNum": "1745", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1745", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "BEG", "operatingCarrier": "FZ", "flightDuration": 21000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Belgrade", "isActive": true, "changeType": "TK", "flightChangeTime": "2/7/2025 8:05:34 AM"}], "chargeInfos": [{"recNum": 2, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 34.65, "curr": "AED", "originalAmt": 34.65, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-03-27T06:19:14", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 34.65, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-03-27T06:19:14"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1280858132, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346849485, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1280858133, "paymentMap": [{"key": "1346849485:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1346849482, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1280858134, "paymentMap": [{"key": "1346849482:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1346849481, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1280858131, "paymentMap": [{"key": "1346849481:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1280858132, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280858132:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1280858134, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": **********, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280858134:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1280858133, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280858133:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1280858131, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280858131:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1346849483, "codeType": "AIR", "amt": -1230, "curr": "AED", "originalAmt": -1230, "originalCurr": "AED", "status": 0, "billDate": "2025-05-15T05:27:53", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1346849483:*********", "paymentID": *********, "amt": -1077, "approveCode": 0}, {"key": "1346849483:*********", "paymentID": *********, "amt": -153, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1230, "curr": "AED", "originalAmt": 1230, "originalCurr": "AED", "status": 0, "billDate": "2025-03-27T06:19:14", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1077, "approveCode": 0}, {"key": "**********:*********", "paymentID": *********, "amt": 153, "approveCode": 0}]}, {"chargeID": 1280864527, "codeType": "PMNT", "amt": 10.94, "curr": "AED", "originalAmt": 10.94, "originalCurr": "AED", "status": 0, "billDate": "2025-03-27T06:19:47", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280864527:*********", "paymentID": *********, "amt": 10.94, "approveCode": 0}]}, {"chargeID": 1346849484, "codeType": "XLGR", "amt": -177, "curr": "AED", "originalAmt": -177, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-05-15T05:27:53", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1280858269, "paymentMap": [{"key": "1346849484:*********", "paymentID": *********, "amt": -177, "approveCode": 0}], "PFID": "181549"}, {"chargeID": 1280858269, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 0, "exchRate": 1, "billDate": "2025-03-27T06:19:14", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181549", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1280858269:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181549"}, {"chargeID": 1280858137, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1280858136, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": **********, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1280858151, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181549"}]}, {"recNum": 3, "charges": [{"chargeID": 1346849500, "codeType": "INSU", "amt": 35.7, "curr": "AED", "originalAmt": 35.7, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-15T05:27:53", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849500:*********", "paymentID": *********, "amt": 35.7, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-15T05:27:53"}, {"chargeID": 1346849487, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346849486, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849487:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1346849490, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346849486, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849490:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346849491, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346849486, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849491:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1346849489, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346849486, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849489:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346849488, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346849486, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849488:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1346849486, "codeType": "AIR", "amt": 1240, "curr": "AED", "originalAmt": 1240, "originalCurr": "AED", "status": 1, "billDate": "2025-05-15T05:27:53", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849486:*********", "paymentID": *********, "amt": 163, "approveCode": 0}, {"key": "1346849486:*********", "paymentID": *********, "amt": 1077, "approveCode": 0}]}, {"chargeID": 1346853622, "codeType": "PMNT", "amt": 1.37, "curr": "AED", "originalAmt": 1.37, "originalCurr": "AED", "status": 1, "billDate": "2025-05-15T05:28:43", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346853622:*********", "paymentID": *********, "amt": 1.37, "approveCode": 0}]}, {"chargeID": 1346849501, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 1, "exchRate": 1, "billDate": "2025-05-15T05:27:53", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181549", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346849501:*********", "paymentID": *********, "amt": 45.7, "approveCode": 0}, {"key": "1346849501:*********", "paymentID": *********, "amt": 131.3, "approveCode": 0}], "PFID": "181549"}, {"chargeID": 1346849493, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1346849486, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346849492, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1346849486, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346849499, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-15T05:27:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181549"}]}, {"recNum": 1, "charges": [{"chargeID": 1280858072, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1202127102, "amt": -75, "curr": "AED", "originalAmt": -75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:13", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127106, "paymentMap": [{"key": "1280858072:*********", "paymentID": *********, "amt": -75, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-03-27T06:19:13"}, {"chargeID": 1280858067, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1202127102, "amt": -45, "curr": "AED", "originalAmt": -45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:13", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127107, "paymentMap": [{"key": "1280858067:*********", "paymentID": *********, "amt": -45, "approveCode": 0}]}, {"chargeID": 1280858068, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1202127102, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127103, "paymentMap": [{"key": "1280858068:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1280858073, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1202127102, "amt": -280, "curr": "AED", "originalAmt": -280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127105, "paymentMap": [{"key": "1280858073:*********", "paymentID": *********, "amt": -280, "approveCode": 0}]}, {"chargeID": 1280858071, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1202127102, "amt": -5, "curr": "AED", "originalAmt": -5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127108, "paymentMap": [{"key": "1280858071:*********", "paymentID": *********, "amt": -5, "approveCode": 0}]}, {"chargeID": 1202127107, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1202127102, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127107:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1202127105, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1202127102, "amt": 280, "curr": "AED", "originalAmt": 280, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127105:*********", "paymentID": *********, "amt": 280, "approveCode": 0}]}, {"chargeID": 1202127108, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1202127102, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127108:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1202127106, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1202127102, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127106:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1202127103, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1202127102, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127103:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1280858070, "codeType": "AIR", "amt": -900, "curr": "AED", "originalAmt": -900, "originalCurr": "AED", "status": 0, "billDate": "2025-03-27T06:19:13", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127102, "paymentMap": [{"key": "1280858070:*********", "paymentID": *********, "amt": -900, "approveCode": 0}]}, {"chargeID": 1202127102, "codeType": "AIR", "amt": 900, "curr": "AED", "originalAmt": 900, "originalCurr": "AED", "status": 0, "billDate": "2025-01-29T08:41:19", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127102:*********", "paymentID": *********, "amt": 900, "approveCode": 0}]}, {"chargeID": 1202134500, "codeType": "PMNT", "amt": 44.61, "curr": "AED", "originalAmt": 44.61, "originalCurr": "AED", "status": 0, "billDate": "2025-01-29T08:41:30", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202134500:*********", "paymentID": *********, "amt": 44.61, "approveCode": 0}]}, {"chargeID": 1280858069, "codeType": "XLGR", "amt": -177, "curr": "AED", "originalAmt": -177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-03-27T06:19:14", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1202127112, "paymentMap": [{"key": "1280858069:*********", "paymentID": *********, "amt": -177, "approveCode": 0}], "PFID": "181557"}, {"chargeID": 1202127112, "codeType": "XLGR", "amt": 177, "curr": "AED", "originalAmt": 177, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181557", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1202127112:*********", "paymentID": *********, "amt": 177, "approveCode": 0}], "PFID": "181557"}, {"chargeID": 1202127109, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1202127102, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1202127104, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1202127102, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1202127113, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 0, "exchRate": 0, "billDate": "2025-01-29T08:41:19", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181557"}]}], "parentPNRs": [], "childPNRs": []}