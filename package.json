{"name": "cover-genius-v1", "version": "1.0.0", "main": "main.js", "scripts": {"start": "node main.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cover-genius", "insurance", "fly<PERSON><PERSON><PERSON>", "pnr", "policy-analysis"], "author": "", "license": "ISC", "description": "Cover Genius Policy Analysis Application - Integrates with FlyDubai and Cover Genius APIs to analyze insurance policies from PNR data", "dependencies": {"axios": "^1.9.0", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "moment": "^2.30.1"}}