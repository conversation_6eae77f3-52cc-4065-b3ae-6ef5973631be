# Cover Genius Policy Analysis Application

This application provides a complete workflow for analyzing Cover Genius insurance policies from FlyDubai PNR data. It integrates with both FlyDubai and Cover Genius APIs to retrieve, process, and generate comprehensive reports.

## Features

- **PNR Data Retrieval**: Fetches passenger reservation data from FlyDubai API
- **Policy ID Extraction**: Automatically extracts Cover Genius policy IDs from PNR data
- **Policy Analysis**: Retrieves detailed policy information from Cover Genius API
- **Advanced Data Processing**:
  - Maps passengers to insurance records using record numbers
  - Links flight segments to insurance purchases
  - Translates status and channel codes to human-readable text
  - Validates policy period compliance
- **Report Generation**: Creates comprehensive HTML reports with insurance coverage analysis
- **Detailed Logging**: Step-by-step progress with analysis summaries
- **Error Handling**: Robust error handling with detailed logging

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Fill in your actual API credentials:
     ```
     # Cover Genius API
     COVER_GENIUS_API_KEY=your_actual_api_key
     COVER_GENIUS_API_SECRET=your_actual_api_secret
     COVER_GENIUS_PARTNER_ID=your_partner_id
     COVER_GENIUS_API_BASE_URL=https://api.covergenius.com/v1

     # FlyDubai API
     FZ_API_BASE_URL=https://api.flydubai.com/v1
     FZ_USERNAME=your_flydubai_username
     FZ_PASSWORD=your_flydubai_password
     ```

## Usage

### Main Application

Run the complete analysis workflow with a PNR number:

```bash
node main.js <PNR_NUMBER>
```

**Example:**
```bash
node main.js ABC123
```

This will:
1. Retrieve PNR data from FlyDubai API
2. Extract Cover Genius policy IDs
3. Fetch policy details from Cover Genius API
4. Generate an HTML report with analysis

### Individual Modules

You can also use the individual modules programmatically:

```javascript
// Integrations module
const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = require('./integrations');

// Functions module
const { extractCoverGeniusPolicyIds } = require('./functions');

// Reports module
const { generateHtmlReport } = require('./reports');
```

## Output Files

The application generates several output files:

- **`RetrievePNR-{PNR}.json`**: Raw PNR data from FlyDubai API
- **`insurance-report-{PNR}-{PolicyID}.html`**: Comprehensive HTML report with analysis

## Report Features

The generated HTML reports include:

- **Summary Dashboard**: Overview of total records, confirmations, and policy compliance
- **Policy Information**: Policy ID, start/end dates, and coverage period
- **Missing Confirmations Table**: Records without confirmation numbers
- **Complete Records Table**: All insurance records with detailed information
- **Visual Indicators**: Color-coded status indicators for easy identification
- **Enhanced Data Fields**:
  - Passenger names linked to insurance records
  - Flight details (origin, destination, flight number)
  - Status translations (ACTIVE, CONFIRMED, etc.)
  - Channel translations (WEB, MOBILE, GDS, etc.)
  - Policy period validation results

## Module Structure

- **`main.js`**: Main application orchestrator
- **`integrations.js`**: API integrations for FlyDubai and Cover Genius
- **`functions.js`**: Utility functions for data extraction and processing
- **`reports.js`**: HTML report generation with styling and formatting

## Error Handling

The application includes comprehensive error handling:

- **API Failures**: Graceful handling of API timeouts and errors
- **Missing Data**: Continues processing even if some data is missing
- **Invalid PNRs**: Validates PNR format before processing
- **Environment Variables**: Checks for required configuration before starting

## Security

- Never commit your `.env` file to version control
- The `.env` file is already included in `.gitignore`
- Use `.env.example` as a template for other developers
- API credentials are securely loaded from environment variables
