# Cover Genius API Integration

This project provides a simple integration with the Cover Genius API to fetch policy details.

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Fill in your actual Cover Genius API credentials:
     ```
     COVER_GENIUS_API_KEY=your_actual_api_key
     COVER_GENIUS_API_SECRET=your_actual_api_secret
     COVER_GENIUS_PARTNER_ID=your_partner_id
     ```

## Usage

```javascript
const { fetchCoverGeniusPolicy } = require('./coverGenius');

// Fetch a policy by ID
fetchCoverGeniusPolicy('RJ6U4-VFXUS-INS')
  .then(response => console.log(response.data))
  .catch(error => console.error(error));
```

## Security

- Never commit your `.env` file to version control
- The `.env` file is already included in `.gitignore`
- Use `.env.example` as a template for other developers
